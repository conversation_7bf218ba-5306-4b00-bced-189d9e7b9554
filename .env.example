# Client Environment Variables (VITE_ prefix for Vite)
VITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.BKJfxPCJFHI-i_m7VE-JqVoUDVNXx2tE0qSDI4JJT2g
VITE_API_BASE_URL=http://localhost:5000/api

# Server Environment Variables
NODE_ENV=development
PORT=5000

# Supabase Configuration (Server)
SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0
VITE_DATABASE_PASSWORD=

# AI Provider Default Configuration (Optional - User will configure their own)
DEFAULT_AI_PROVIDER=openrouter
DEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash
DEFAULT_GENERATION_MODEL=google/gemini-2.5-pro

# Security
JWT_SECRET=your-jwt-secret-change-this-in-production
SESSION_SECRET=your-session-secret-change-this-in-production

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# Database Configuration (Optional - for SQLite local storage)
DATABASE_URL=./data/chewyai.sqlite 