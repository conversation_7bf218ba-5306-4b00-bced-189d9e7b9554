2025-06-02 16:24:36.654 [info] [main] Log level: Info
2025-06-02 16:24:36.654 [info] [main] Validating found git in: "git"
2025-06-02 16:24:36.654 [info] [main] Using git "2.47.2" from "git"
2025-06-02 16:24:36.654 [info] [Model][doInitialScan] Initial repository scan started
2025-06-02 16:24:36.654 [info] > git rev-parse --show-toplevel [6ms]
2025-06-02 16:24:36.654 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-02 16:24:36.654 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-02 16:24:36.654 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-02 16:24:36.654 [info] > git config --get commit.template [25ms]
2025-06-02 16:24:36.654 [info] > git rev-parse --show-toplevel [12ms]
2025-06-02 16:24:36.654 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-02 16:24:36.654 [info] > git rev-parse --show-toplevel [9ms]
2025-06-02 16:24:36.656 [info] > git status -z -uall [15ms]
2025-06-02 16:24:36.656 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:24:36.687 [info] > git rev-parse --show-toplevel [52ms]
2025-06-02 16:24:36.729 [info] > git rev-parse --show-toplevel [17ms]
2025-06-02 16:24:36.730 [info] > git config --get commit.template [21ms]
2025-06-02 16:24:36.730 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [46ms]
2025-06-02 16:24:36.745 [info] > git config --get --local branch.main.vscode-merge-base [7ms]
2025-06-02 16:24:36.745 [warning] [Git][config] git config failed: Failed to execute git
2025-06-02 16:24:36.768 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [24ms]
2025-06-02 16:24:36.769 [info] > git rev-parse --show-toplevel [29ms]
2025-06-02 16:24:36.825 [info] > git status -z -uall [47ms]
2025-06-02 16:24:36.825 [info] > git reflog main --grep-reflog=branch: Created from *. [63ms]
2025-06-02 16:24:36.845 [info] > git rev-parse --show-toplevel [71ms]
2025-06-02 16:24:36.905 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [85ms]
2025-06-02 16:24:36.913 [info] > git rev-parse --show-toplevel [8ms]
2025-06-02 16:24:36.914 [info] > git fetch [337ms]
2025-06-02 16:24:36.914 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-02 16:24:36.915 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [71ms]
2025-06-02 16:24:37.096 [info] > git rev-parse --show-toplevel [175ms]
2025-06-02 16:24:37.097 [info] > git config --add --local branch.main.vscode-merge-base origin/main [178ms]
2025-06-02 16:24:37.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [186ms]
2025-06-02 16:24:37.109 [info] > git config --get --local branch.main.vscode-merge-base [9ms]
2025-06-02 16:24:37.112 [info] > git config --get commit.template [181ms]
2025-06-02 16:24:37.122 [info] > git config --get commit.template [14ms]
2025-06-02 16:24:37.122 [info] > git rev-parse --show-toplevel [17ms]
2025-06-02 16:24:37.123 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:24:37.123 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [11ms]
2025-06-02 16:24:37.133 [info] > git rev-parse --show-toplevel [3ms]
2025-06-02 16:24:37.133 [info] > git merge-base refs/heads/main refs/remotes/origin/main [7ms]
2025-06-02 16:24:37.148 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-02 16:24:37.159 [info] > git rev-parse --show-toplevel [16ms]
2025-06-02 16:24:37.191 [info] > git merge-base refs/heads/main refs/remotes/origin/main [34ms]
2025-06-02 16:24:37.201 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [61ms]
2025-06-02 16:24:37.202 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [1ms]
2025-06-02 16:24:37.203 [info] > git rev-parse --show-toplevel [40ms]
2025-06-02 16:24:37.216 [info] > git status -z -uall [6ms]
2025-06-02 16:24:37.216 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:24:37.219 [info] > git rev-parse --show-toplevel [4ms]
2025-06-02 16:24:37.223 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-02 16:24:37.229 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-02 16:24:39.976 [info] > git config --get --local branch.main.github-pr-owner-number [156ms]
2025-06-02 16:24:39.976 [warning] [Git][config] git config failed: Failed to execute git
2025-06-02 16:24:43.875 [info] > git config --get commit.template [4ms]
2025-06-02 16:24:43.877 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:24:44.284 [info] > git status -z -uall [5ms]
2025-06-02 16:24:44.285 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:24:50.000 [info] > git config --get commit.template [6ms]
2025-06-02 16:24:50.001 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:24:50.010 [info] > git status -z -uall [5ms]
2025-06-02 16:24:50.011 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:24:55.021 [info] > git config --get commit.template [2ms]
2025-06-02 16:24:55.027 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:24:55.043 [info] > git status -z -uall [12ms]
2025-06-02 16:24:55.044 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:25:00.057 [info] > git config --get commit.template [5ms]
2025-06-02 16:25:00.058 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:25:00.069 [info] > git status -z -uall [4ms]
2025-06-02 16:25:00.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:25:05.081 [info] > git config --get commit.template [4ms]
2025-06-02 16:25:05.082 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:25:05.095 [info] > git status -z -uall [9ms]
2025-06-02 16:25:05.096 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:25:10.124 [info] > git config --get commit.template [10ms]
2025-06-02 16:25:10.126 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:25:10.191 [info] > git status -z -uall [32ms]
2025-06-02 16:25:10.191 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:25:15.214 [info] > git config --get commit.template [6ms]
2025-06-02 16:25:15.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:25:15.225 [info] > git status -z -uall [6ms]
2025-06-02 16:25:15.226 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:25:20.245 [info] > git config --get commit.template [6ms]
2025-06-02 16:25:20.245 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:25:20.259 [info] > git status -z -uall [7ms]
2025-06-02 16:25:20.261 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:25:25.276 [info] > git config --get commit.template [4ms]
2025-06-02 16:25:25.278 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:25:25.286 [info] > git status -z -uall [4ms]
2025-06-02 16:25:25.288 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:25:30.302 [info] > git config --get commit.template [5ms]
2025-06-02 16:25:30.303 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:25:30.310 [info] > git status -z -uall [4ms]
2025-06-02 16:25:30.311 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:25:36.510 [info] > git config --get commit.template [8ms]
2025-06-02 16:25:36.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:25:36.525 [info] > git status -z -uall [7ms]
2025-06-02 16:25:36.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:25:41.596 [info] > git config --get commit.template [2ms]
2025-06-02 16:25:41.606 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:25:41.632 [info] > git status -z -uall [15ms]
2025-06-02 16:25:41.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 16:25:46.651 [info] > git config --get commit.template [7ms]
2025-06-02 16:25:46.652 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:25:46.664 [info] > git status -z -uall [5ms]
2025-06-02 16:25:46.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:25:51.731 [info] > git config --get commit.template [16ms]
2025-06-02 16:25:51.733 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:25:51.752 [info] > git status -z -uall [10ms]
2025-06-02 16:25:51.754 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:26:02.590 [info] > git config --get commit.template [7ms]
2025-06-02 16:26:02.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:26:02.608 [info] > git status -z -uall [10ms]
2025-06-02 16:26:02.609 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:26:07.205 [info] > git show --textconv :Makefile [7ms]
2025-06-02 16:26:07.206 [info] > git ls-files --stage -- Makefile [1ms]
2025-06-02 16:26:07.216 [info] > git cat-file -s 016f5821d5edfc54001ab9a7da3ad02039756559 [2ms]
2025-06-02 16:26:07.627 [info] > git config --get commit.template [7ms]
2025-06-02 16:26:07.628 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:26:07.640 [info] > git status -z -uall [7ms]
2025-06-02 16:26:07.641 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:26:12.701 [info] > git config --get commit.template [12ms]
2025-06-02 16:26:12.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:26:12.721 [info] > git status -z -uall [9ms]
2025-06-02 16:26:12.722 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:26:17.750 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:26:17.752 [info] > git config --get commit.template [15ms]
2025-06-02 16:26:17.789 [info] > git status -z -uall [16ms]
2025-06-02 16:26:17.791 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:26:22.811 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:26:22.811 [info] > git config --get commit.template [8ms]
2025-06-02 16:26:22.826 [info] > git status -z -uall [6ms]
2025-06-02 16:26:22.827 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:26:27.840 [info] > git config --get commit.template [3ms]
2025-06-02 16:26:27.848 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:26:27.861 [info] > git status -z -uall [6ms]
2025-06-02 16:26:27.863 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:26:32.880 [info] > git config --get commit.template [6ms]
2025-06-02 16:26:32.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:26:32.893 [info] > git status -z -uall [6ms]
2025-06-02 16:26:32.894 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:26:37.916 [info] > git config --get commit.template [9ms]
2025-06-02 16:26:37.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:26:37.933 [info] > git status -z -uall [7ms]
2025-06-02 16:26:37.934 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:26:42.950 [info] > git config --get commit.template [6ms]
2025-06-02 16:26:42.951 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:26:42.972 [info] > git status -z -uall [12ms]
2025-06-02 16:26:42.973 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:26:47.994 [info] > git config --get commit.template [6ms]
2025-06-02 16:26:47.996 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:26:48.009 [info] > git status -z -uall [7ms]
2025-06-02 16:26:48.009 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:26:53.028 [info] > git config --get commit.template [7ms]
2025-06-02 16:26:53.030 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:26:53.041 [info] > git status -z -uall [5ms]
2025-06-02 16:26:53.042 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:26:58.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:26:58.064 [info] > git config --get commit.template [11ms]
2025-06-02 16:26:58.083 [info] > git status -z -uall [7ms]
2025-06-02 16:26:58.084 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:27:03.101 [info] > git config --get commit.template [1ms]
2025-06-02 16:27:03.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:27:03.144 [info] > git status -z -uall [17ms]
2025-06-02 16:27:03.145 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:27:08.166 [info] > git config --get commit.template [8ms]
2025-06-02 16:27:08.167 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:27:08.186 [info] > git status -z -uall [12ms]
2025-06-02 16:27:08.188 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:27:13.209 [info] > git config --get commit.template [9ms]
2025-06-02 16:27:13.211 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:27:13.235 [info] > git status -z -uall [14ms]
2025-06-02 16:27:13.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:27:16.673 [info] > git show --textconv :env.example [7ms]
2025-06-02 16:27:16.674 [info] > git ls-files --stage -- env.example [3ms]
2025-06-02 16:27:16.682 [info] > git cat-file -s 14b7aaed2743fe047f63728a1ea5dc1466d8c28b [2ms]
2025-06-02 16:27:18.267 [info] > git config --get commit.template [11ms]
2025-06-02 16:27:18.268 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:27:18.280 [info] > git status -z -uall [6ms]
2025-06-02 16:27:18.282 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:27:23.304 [info] > git config --get commit.template [10ms]
2025-06-02 16:27:23.305 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:27:23.322 [info] > git status -z -uall [8ms]
2025-06-02 16:27:23.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:27:25.105 [info] > git show --textconv :.env.example [63ms]
2025-06-02 16:27:25.105 [info] > git ls-files --stage -- .env.example [56ms]
2025-06-02 16:27:25.115 [info] > git cat-file -s d814a3cfbdb4b5bf5877f10918b72f0aef0a2980 [2ms]
2025-06-02 16:27:28.340 [info] > git config --get commit.template [6ms]
2025-06-02 16:27:28.341 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:27:28.352 [info] > git status -z -uall [5ms]
2025-06-02 16:27:28.353 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:27:33.373 [info] > git config --get commit.template [8ms]
2025-06-02 16:27:33.375 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:27:33.390 [info] > git status -z -uall [7ms]
2025-06-02 16:27:33.395 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 16:27:37.215 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- env.example [4ms]
2025-06-02 16:27:38.416 [info] > git config --get commit.template [7ms]
2025-06-02 16:27:38.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:27:38.434 [info] > git status -z -uall [9ms]
2025-06-02 16:27:38.436 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:27:43.455 [info] > git config --get commit.template [8ms]
2025-06-02 16:27:43.456 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:27:43.472 [info] > git status -z -uall [7ms]
2025-06-02 16:27:43.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:27:48.541 [info] > git config --get commit.template [27ms]
2025-06-02 16:27:48.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-06-02 16:27:48.645 [info] > git status -z -uall [34ms]
2025-06-02 16:27:48.648 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-02 16:27:53.670 [info] > git config --get commit.template [1ms]
2025-06-02 16:27:53.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-02 16:27:53.732 [info] > git status -z -uall [15ms]
2025-06-02 16:27:53.733 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:27:55.668 [info] > git ls-files --stage -- env.example [8ms]
2025-06-02 16:27:55.674 [info] > git ls-files --stage -- Makefile [22ms]
2025-06-02 16:27:55.681 [info] > git cat-file -s 14b7aaed2743fe047f63728a1ea5dc1466d8c28b [7ms]
2025-06-02 16:27:55.682 [info] > git ls-files --stage -- .env.example [16ms]
2025-06-02 16:27:55.689 [info] > git cat-file -s 016f5821d5edfc54001ab9a7da3ad02039756559 [8ms]
2025-06-02 16:27:55.690 [info] > git cat-file -s d814a3cfbdb4b5bf5877f10918b72f0aef0a2980 [2ms]
2025-06-02 16:27:55.782 [info] > git show --textconv :env.example [15ms]
2025-06-02 16:27:55.782 [info] > git show --textconv :Makefile [8ms]
2025-06-02 16:27:55.785 [info] > git show --textconv :.env.example [4ms]
2025-06-02 16:27:58.752 [info] > git config --get commit.template [7ms]
2025-06-02 16:27:58.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:27:58.768 [info] > git status -z -uall [6ms]
2025-06-02 16:27:58.771 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:28:03.788 [info] > git config --get commit.template [2ms]
2025-06-02 16:28:03.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:28:03.819 [info] > git status -z -uall [8ms]
2025-06-02 16:28:03.820 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:28:07.799 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- .env.example [8ms]
2025-06-02 16:28:08.842 [info] > git config --get commit.template [11ms]
2025-06-02 16:28:08.843 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:28:08.857 [info] > git status -z -uall [6ms]
2025-06-02 16:28:08.859 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:28:13.873 [info] > git config --get commit.template [6ms]
2025-06-02 16:28:13.874 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:28:13.886 [info] > git status -z -uall [5ms]
2025-06-02 16:28:13.887 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:28:18.904 [info] > git config --get commit.template [3ms]
2025-06-02 16:28:18.916 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:28:18.933 [info] > git status -z -uall [8ms]
2025-06-02 16:28:18.934 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:28:23.951 [info] > git config --get commit.template [6ms]
2025-06-02 16:28:23.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:28:23.963 [info] > git status -z -uall [5ms]
2025-06-02 16:28:23.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:28:28.996 [info] > git config --get commit.template [12ms]
2025-06-02 16:28:28.998 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:28:29.031 [info] > git status -z -uall [14ms]
2025-06-02 16:28:29.032 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:28:34.098 [info] > git config --get commit.template [2ms]
2025-06-02 16:28:34.118 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 16:28:34.138 [info] > git status -z -uall [8ms]
2025-06-02 16:28:34.140 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:28:39.166 [info] > git config --get commit.template [12ms]
2025-06-02 16:28:39.167 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:28:39.188 [info] > git status -z -uall [11ms]
2025-06-02 16:28:39.189 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:28:44.215 [info] > git config --get commit.template [12ms]
2025-06-02 16:28:44.219 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 16:28:44.241 [info] > git status -z -uall [11ms]
2025-06-02 16:28:44.244 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:28:49.261 [info] > git config --get commit.template [6ms]
2025-06-02 16:28:49.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:28:49.273 [info] > git status -z -uall [5ms]
2025-06-02 16:28:49.274 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:28:54.293 [info] > git config --get commit.template [8ms]
2025-06-02 16:28:54.294 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:28:54.305 [info] > git status -z -uall [6ms]
2025-06-02 16:28:54.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:28:59.323 [info] > git config --get commit.template [6ms]
2025-06-02 16:28:59.324 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:28:59.335 [info] > git status -z -uall [5ms]
2025-06-02 16:28:59.336 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:29:04.353 [info] > git config --get commit.template [5ms]
2025-06-02 16:29:04.354 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:29:04.365 [info] > git status -z -uall [4ms]
2025-06-02 16:29:04.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:29:09.394 [info] > git config --get commit.template [14ms]
2025-06-02 16:29:09.396 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:29:09.429 [info] > git status -z -uall [19ms]
2025-06-02 16:29:09.432 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:29:14.460 [info] > git config --get commit.template [11ms]
2025-06-02 16:29:14.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:29:14.513 [info] > git status -z -uall [13ms]
2025-06-02 16:29:14.517 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:29:19.538 [info] > git config --get commit.template [8ms]
2025-06-02 16:29:19.555 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:29:19.587 [info] > git status -z -uall [18ms]
2025-06-02 16:29:19.587 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:29:24.616 [info] > git config --get commit.template [13ms]
2025-06-02 16:29:24.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:29:24.641 [info] > git status -z -uall [11ms]
2025-06-02 16:29:24.643 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:29:29.661 [info] > git config --get commit.template [7ms]
2025-06-02 16:29:29.662 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:29:29.674 [info] > git status -z -uall [6ms]
2025-06-02 16:29:29.675 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:29:34.697 [info] > git config --get commit.template [11ms]
2025-06-02 16:29:34.698 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:29:34.713 [info] > git status -z -uall [7ms]
2025-06-02 16:29:34.714 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:29:39.735 [info] > git config --get commit.template [4ms]
2025-06-02 16:29:39.746 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:29:39.760 [info] > git status -z -uall [6ms]
2025-06-02 16:29:39.761 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:29:44.785 [info] > git config --get commit.template [10ms]
2025-06-02 16:29:44.786 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:29:44.802 [info] > git status -z -uall [8ms]
2025-06-02 16:29:44.803 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:29:49.823 [info] > git config --get commit.template [1ms]
2025-06-02 16:29:49.858 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:29:49.880 [info] > git status -z -uall [9ms]
2025-06-02 16:29:49.882 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:29:54.915 [info] > git config --get commit.template [14ms]
2025-06-02 16:29:54.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:29:54.959 [info] > git status -z -uall [17ms]
2025-06-02 16:29:54.960 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:29:59.980 [info] > git config --get commit.template [8ms]
2025-06-02 16:29:59.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:29:59.995 [info] > git status -z -uall [8ms]
2025-06-02 16:29:59.997 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:30:05.019 [info] > git config --get commit.template [7ms]
2025-06-02 16:30:05.020 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:30:05.036 [info] > git status -z -uall [9ms]
2025-06-02 16:30:05.038 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:30:10.295 [info] > git config --get commit.template [10ms]
2025-06-02 16:30:10.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:30:10.326 [info] > git status -z -uall [11ms]
2025-06-02 16:30:10.327 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:30:15.351 [info] > git config --get commit.template [8ms]
2025-06-02 16:30:15.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:30:15.364 [info] > git status -z -uall [6ms]
2025-06-02 16:30:15.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:30:20.390 [info] > git config --get commit.template [10ms]
2025-06-02 16:30:20.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:30:20.410 [info] > git status -z -uall [9ms]
2025-06-02 16:30:20.411 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:30:25.439 [info] > git config --get commit.template [13ms]
2025-06-02 16:30:25.440 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:30:25.473 [info] > git status -z -uall [21ms]
2025-06-02 16:30:25.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:30:30.515 [info] > git config --get commit.template [20ms]
2025-06-02 16:30:30.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:30:30.561 [info] > git status -z -uall [26ms]
2025-06-02 16:30:30.565 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 16:30:35.611 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:30:35.611 [info] > git config --get commit.template [23ms]
2025-06-02 16:30:35.647 [info] > git status -z -uall [20ms]
2025-06-02 16:30:35.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-02 16:30:40.693 [info] > git config --get commit.template [16ms]
2025-06-02 16:30:40.694 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:30:40.728 [info] > git status -z -uall [15ms]
2025-06-02 16:30:40.731 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:30:45.754 [info] > git config --get commit.template [8ms]
2025-06-02 16:30:45.755 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:30:45.769 [info] > git status -z -uall [6ms]
2025-06-02 16:30:45.771 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:30:50.789 [info] > git config --get commit.template [7ms]
2025-06-02 16:30:50.790 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:30:50.805 [info] > git status -z -uall [8ms]
2025-06-02 16:30:50.807 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:31:04.246 [info] > git config --get commit.template [9ms]
2025-06-02 16:31:04.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:31:04.274 [info] > git status -z -uall [15ms]
2025-06-02 16:31:04.278 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:31:25.522 [info] > git config --get commit.template [6ms]
2025-06-02 16:31:25.522 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:31:25.538 [info] > git status -z -uall [8ms]
2025-06-02 16:31:25.538 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:31:30.555 [info] > git config --get commit.template [5ms]
2025-06-02 16:31:30.556 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:31:30.567 [info] > git status -z -uall [5ms]
2025-06-02 16:31:30.569 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:31:35.605 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 16:31:35.606 [info] > git config --get commit.template [20ms]
2025-06-02 16:31:35.644 [info] > git status -z -uall [21ms]
2025-06-02 16:31:35.644 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:31:40.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:31:40.700 [info] > git config --get commit.template [26ms]
2025-06-02 16:31:40.764 [info] > git status -z -uall [33ms]
2025-06-02 16:31:40.764 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 16:31:45.795 [info] > git config --get commit.template [11ms]
2025-06-02 16:31:45.796 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:31:45.823 [info] > git status -z -uall [12ms]
2025-06-02 16:31:45.824 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:31:50.850 [info] > git config --get commit.template [9ms]
2025-06-02 16:31:50.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:31:50.872 [info] > git status -z -uall [10ms]
2025-06-02 16:31:50.874 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:31:55.896 [info] > git config --get commit.template [9ms]
2025-06-02 16:31:55.897 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:31:55.915 [info] > git status -z -uall [7ms]
2025-06-02 16:31:55.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:32:00.933 [info] > git config --get commit.template [2ms]
2025-06-02 16:32:00.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:32:00.954 [info] > git status -z -uall [7ms]
2025-06-02 16:32:00.955 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:32:05.973 [info] > git config --get commit.template [6ms]
2025-06-02 16:32:05.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:32:05.985 [info] > git status -z -uall [5ms]
2025-06-02 16:32:05.986 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:32:11.017 [info] > git config --get commit.template [17ms]
2025-06-02 16:32:11.020 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:32:11.109 [info] > git status -z -uall [52ms]
2025-06-02 16:32:11.109 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-02 16:32:16.144 [info] > git config --get commit.template [4ms]
2025-06-02 16:32:16.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 16:32:16.216 [info] > git status -z -uall [16ms]
2025-06-02 16:32:16.218 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:32:21.248 [info] > git config --get commit.template [12ms]
2025-06-02 16:32:21.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:32:21.271 [info] > git status -z -uall [12ms]
2025-06-02 16:32:21.273 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:32:26.301 [info] > git config --get commit.template [7ms]
2025-06-02 16:32:26.336 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-02 16:32:26.372 [info] > git status -z -uall [18ms]
2025-06-02 16:32:26.375 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:32:31.397 [info] > git config --get commit.template [8ms]
2025-06-02 16:32:31.398 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:32:31.412 [info] > git status -z -uall [8ms]
2025-06-02 16:32:31.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:32:36.435 [info] > git config --get commit.template [9ms]
2025-06-02 16:32:36.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:32:36.451 [info] > git status -z -uall [6ms]
2025-06-02 16:32:36.452 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:32:41.481 [info] > git config --get commit.template [12ms]
2025-06-02 16:32:41.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:32:41.504 [info] > git status -z -uall [12ms]
2025-06-02 16:32:41.505 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:32:46.524 [info] > git config --get commit.template [7ms]
2025-06-02 16:32:46.525 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:32:46.538 [info] > git status -z -uall [6ms]
2025-06-02 16:32:46.539 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:32:51.564 [info] > git config --get commit.template [11ms]
2025-06-02 16:32:51.565 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:32:51.583 [info] > git status -z -uall [7ms]
2025-06-02 16:32:51.584 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:32:57.249 [info] > git config --get commit.template [5ms]
2025-06-02 16:32:57.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:32:57.261 [info] > git status -z -uall [5ms]
2025-06-02 16:32:57.262 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:32:57.487 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [83ms]
2025-06-02 16:33:54.031 [info] > git config --get commit.template [2ms]
2025-06-02 16:33:54.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:33:54.054 [info] > git status -z -uall [8ms]
2025-06-02 16:33:54.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:34:02.378 [info] > git config --get commit.template [13ms]
2025-06-02 16:34:02.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:34:02.401 [info] > git status -z -uall [9ms]
2025-06-02 16:34:02.403 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:34:09.986 [info] > git config --get commit.template [108ms]
2025-06-02 16:34:10.006 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:34:10.042 [info] > git status -z -uall [21ms]
2025-06-02 16:34:10.045 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-02 16:34:15.106 [info] > git config --get commit.template [21ms]
2025-06-02 16:34:15.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:34:15.192 [info] > git status -z -uall [74ms]
2025-06-02 16:34:15.193 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [64ms]
2025-06-02 16:34:26.380 [info] > git config --get commit.template [8ms]
2025-06-02 16:34:26.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:34:26.395 [info] > git status -z -uall [6ms]
2025-06-02 16:34:26.396 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:34:47.859 [info] > git config --get commit.template [12ms]
2025-06-02 16:34:47.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:34:47.876 [info] > git status -z -uall [8ms]
2025-06-02 16:34:47.878 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:34:52.906 [info] > git config --get commit.template [12ms]
2025-06-02 16:34:52.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:34:52.926 [info] > git status -z -uall [10ms]
2025-06-02 16:34:52.927 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:34:57.947 [info] > git config --get commit.template [6ms]
2025-06-02 16:34:57.948 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:34:57.961 [info] > git status -z -uall [7ms]
2025-06-02 16:34:57.963 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:35:02.999 [info] > git config --get commit.template [14ms]
2025-06-02 16:35:03.001 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:35:03.031 [info] > git status -z -uall [15ms]
2025-06-02 16:35:03.032 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:35:08.072 [info] > git config --get commit.template [16ms]
2025-06-02 16:35:08.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:35:08.119 [info] > git status -z -uall [28ms]
2025-06-02 16:35:08.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:35:13.149 [info] > git config --get commit.template [11ms]
2025-06-02 16:35:13.149 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:35:13.174 [info] > git status -z -uall [14ms]
2025-06-02 16:35:13.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:35:18.207 [info] > git config --get commit.template [12ms]
2025-06-02 16:35:18.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:35:18.226 [info] > git status -z -uall [9ms]
2025-06-02 16:35:18.227 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:35:23.253 [info] > git config --get commit.template [11ms]
2025-06-02 16:35:23.254 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:35:23.285 [info] > git status -z -uall [19ms]
2025-06-02 16:35:23.286 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:35:28.308 [info] > git config --get commit.template [8ms]
2025-06-02 16:35:28.309 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:35:28.329 [info] > git status -z -uall [11ms]
2025-06-02 16:35:28.330 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:35:33.359 [info] > git config --get commit.template [14ms]
2025-06-02 16:35:33.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:35:33.392 [info] > git status -z -uall [16ms]
2025-06-02 16:35:33.393 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:35:38.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:35:38.424 [info] > git config --get commit.template [14ms]
2025-06-02 16:35:38.449 [info] > git status -z -uall [9ms]
2025-06-02 16:35:38.451 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:35:43.484 [info] > git config --get commit.template [15ms]
2025-06-02 16:35:43.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:35:43.516 [info] > git status -z -uall [17ms]
2025-06-02 16:35:43.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:35:48.553 [info] > git config --get commit.template [14ms]
2025-06-02 16:35:48.553 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:35:48.584 [info] > git status -z -uall [15ms]
2025-06-02 16:35:48.584 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:35:53.613 [info] > git config --get commit.template [11ms]
2025-06-02 16:35:53.614 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:35:53.631 [info] > git status -z -uall [8ms]
2025-06-02 16:35:53.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:35:58.653 [info] > git config --get commit.template [2ms]
2025-06-02 16:35:58.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:35:58.709 [info] > git status -z -uall [17ms]
2025-06-02 16:35:58.710 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:36:23.394 [info] > git config --get commit.template [3ms]
2025-06-02 16:36:23.425 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:36:23.480 [info] > git status -z -uall [28ms]
2025-06-02 16:36:23.480 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:36:41.080 [info] > git config --get commit.template [21ms]
2025-06-02 16:36:41.081 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:36:41.103 [info] > git status -z -uall [10ms]
2025-06-02 16:36:41.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:36:46.146 [info] > git config --get commit.template [19ms]
2025-06-02 16:36:46.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:36:46.185 [info] > git status -z -uall [21ms]
2025-06-02 16:36:46.186 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:36:51.244 [info] > git config --get commit.template [27ms]
2025-06-02 16:36:51.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:36:51.312 [info] > git status -z -uall [28ms]
2025-06-02 16:36:51.315 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 16:36:56.403 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:36:56.403 [info] > git config --get commit.template [55ms]
2025-06-02 16:36:56.473 [info] > git status -z -uall [42ms]
2025-06-02 16:36:56.479 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 16:37:01.515 [info] > git config --get commit.template [13ms]
2025-06-02 16:37:01.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:37:01.545 [info] > git status -z -uall [18ms]
2025-06-02 16:37:01.546 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:37:06.575 [info] > git config --get commit.template [9ms]
2025-06-02 16:37:06.576 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:37:06.595 [info] > git status -z -uall [11ms]
2025-06-02 16:37:06.596 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:37:11.630 [info] > git config --get commit.template [15ms]
2025-06-02 16:37:11.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:37:11.666 [info] > git status -z -uall [18ms]
2025-06-02 16:37:11.667 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:37:19.972 [info] > git config --get commit.template [16ms]
2025-06-02 16:37:19.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:37:20.018 [info] > git status -z -uall [26ms]
2025-06-02 16:37:20.020 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:37:25.059 [info] > git config --get commit.template [15ms]
2025-06-02 16:37:25.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 16:37:25.094 [info] > git status -z -uall [15ms]
2025-06-02 16:37:25.095 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:37:30.124 [info] > git config --get commit.template [11ms]
2025-06-02 16:37:30.125 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:37:30.142 [info] > git status -z -uall [7ms]
2025-06-02 16:37:30.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:37:35.175 [info] > git config --get commit.template [12ms]
2025-06-02 16:37:35.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:37:35.200 [info] > git status -z -uall [11ms]
2025-06-02 16:37:35.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:37:40.246 [info] > git config --get commit.template [21ms]
2025-06-02 16:37:40.247 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:37:40.288 [info] > git status -z -uall [23ms]
2025-06-02 16:37:40.289 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:37:45.327 [info] > git config --get commit.template [15ms]
2025-06-02 16:37:45.329 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:37:45.373 [info] > git status -z -uall [24ms]
2025-06-02 16:37:45.375 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:37:50.434 [info] > git config --get commit.template [30ms]
2025-06-02 16:37:50.435 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:37:50.489 [info] > git status -z -uall [23ms]
2025-06-02 16:37:50.489 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:37:55.564 [info] > git config --get commit.template [39ms]
2025-06-02 16:37:55.567 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:37:55.630 [info] > git status -z -uall [32ms]
2025-06-02 16:37:55.631 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:38:00.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:38:00.687 [info] > git config --get commit.template [32ms]
2025-06-02 16:38:00.797 [info] > git status -z -uall [90ms]
2025-06-02 16:38:00.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [65ms]
2025-06-02 16:38:05.828 [info] > git config --get commit.template [10ms]
2025-06-02 16:38:05.830 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:38:05.855 [info] > git status -z -uall [10ms]
2025-06-02 16:38:05.857 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:38:10.901 [info] > git config --get commit.template [16ms]
2025-06-02 16:38:10.921 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:38:10.947 [info] > git status -z -uall [13ms]
2025-06-02 16:38:10.947 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:38:15.978 [info] > git config --get commit.template [13ms]
2025-06-02 16:38:15.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:38:16.012 [info] > git status -z -uall [14ms]
2025-06-02 16:38:16.012 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:38:21.043 [info] > git config --get commit.template [12ms]
2025-06-02 16:38:21.044 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:38:21.067 [info] > git status -z -uall [11ms]
2025-06-02 16:38:21.068 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:38:26.107 [info] > git config --get commit.template [17ms]
2025-06-02 16:38:26.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:38:26.149 [info] > git status -z -uall [20ms]
2025-06-02 16:38:26.151 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:38:31.181 [info] > git config --get commit.template [1ms]
2025-06-02 16:38:31.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:38:31.265 [info] > git status -z -uall [32ms]
2025-06-02 16:38:31.269 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 16:38:36.322 [info] > git config --get commit.template [2ms]
2025-06-02 16:38:36.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:38:36.399 [info] > git status -z -uall [11ms]
2025-06-02 16:38:36.401 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:38:41.444 [info] > git config --get commit.template [21ms]
2025-06-02 16:38:41.445 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:38:41.474 [info] > git status -z -uall [14ms]
2025-06-02 16:38:41.475 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:38:46.514 [info] > git config --get commit.template [17ms]
2025-06-02 16:38:46.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:38:46.558 [info] > git status -z -uall [24ms]
2025-06-02 16:38:46.560 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:38:51.589 [info] > git config --get commit.template [2ms]
2025-06-02 16:38:51.614 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:38:51.658 [info] > git status -z -uall [23ms]
2025-06-02 16:38:51.658 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:38:56.689 [info] > git config --get commit.template [13ms]
2025-06-02 16:38:56.690 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:38:56.714 [info] > git status -z -uall [11ms]
2025-06-02 16:38:56.716 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:39:01.746 [info] > git config --get commit.template [12ms]
2025-06-02 16:39:01.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:39:01.768 [info] > git status -z -uall [10ms]
2025-06-02 16:39:01.770 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:39:06.864 [info] > git config --get commit.template [15ms]
2025-06-02 16:39:06.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:39:06.889 [info] > git status -z -uall [12ms]
2025-06-02 16:39:06.889 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:39:11.920 [info] > git config --get commit.template [12ms]
2025-06-02 16:39:11.922 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:39:11.948 [info] > git status -z -uall [13ms]
2025-06-02 16:39:11.949 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:39:17.004 [info] > git config --get commit.template [25ms]
2025-06-02 16:39:17.005 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:39:17.047 [info] > git status -z -uall [23ms]
2025-06-02 16:39:17.050 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:39:22.111 [info] > git config --get commit.template [25ms]
2025-06-02 16:39:22.113 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:39:22.153 [info] > git status -z -uall [22ms]
2025-06-02 16:39:22.156 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 16:39:27.198 [info] > git config --get commit.template [24ms]
2025-06-02 16:39:27.199 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:39:27.234 [info] > git status -z -uall [19ms]
2025-06-02 16:39:27.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:39:32.270 [info] > git config --get commit.template [14ms]
2025-06-02 16:39:32.271 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:39:32.403 [info] > git status -z -uall [117ms]
2025-06-02 16:39:32.406 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [107ms]
2025-06-02 16:39:37.468 [info] > git config --get commit.template [2ms]
2025-06-02 16:39:37.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:39:37.537 [info] > git status -z -uall [27ms]
2025-06-02 16:39:37.539 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:39:42.598 [info] > git config --get commit.template [27ms]
2025-06-02 16:39:42.600 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:39:42.660 [info] > git status -z -uall [26ms]
2025-06-02 16:39:42.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:39:47.705 [info] > git config --get commit.template [19ms]
2025-06-02 16:39:47.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:39:47.751 [info] > git status -z -uall [21ms]
2025-06-02 16:39:47.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:39:52.835 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:39:52.836 [info] > git config --get commit.template [37ms]
2025-06-02 16:39:52.901 [info] > git status -z -uall [27ms]
2025-06-02 16:39:52.905 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:39:57.939 [info] > git config --get commit.template [1ms]
2025-06-02 16:39:57.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:39:58.031 [info] > git status -z -uall [23ms]
2025-06-02 16:39:58.032 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:40:03.095 [info] > git config --get commit.template [25ms]
2025-06-02 16:40:03.099 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 16:40:03.151 [info] > git status -z -uall [29ms]
2025-06-02 16:40:03.155 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 16:40:08.245 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:40:08.246 [info] > git config --get commit.template [48ms]
2025-06-02 16:40:08.337 [info] > git status -z -uall [35ms]
2025-06-02 16:40:08.339 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:40:13.399 [info] > git config --get commit.template [26ms]
2025-06-02 16:40:13.400 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:40:13.463 [info] > git status -z -uall [36ms]
2025-06-02 16:40:13.469 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 16:40:18.512 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:40:18.512 [info] > git config --get commit.template [20ms]
2025-06-02 16:40:18.545 [info] > git status -z -uall [14ms]
2025-06-02 16:40:18.547 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:40:23.594 [info] > git config --get commit.template [20ms]
2025-06-02 16:40:23.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:40:23.636 [info] > git status -z -uall [19ms]
2025-06-02 16:40:23.638 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:40:28.672 [info] > git config --get commit.template [15ms]
2025-06-02 16:40:28.673 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:40:28.695 [info] > git status -z -uall [10ms]
2025-06-02 16:40:28.696 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:40:33.733 [info] > git config --get commit.template [14ms]
2025-06-02 16:40:33.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:40:33.760 [info] > git status -z -uall [13ms]
2025-06-02 16:40:33.761 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:40:38.808 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:40:38.808 [info] > git config --get commit.template [26ms]
2025-06-02 16:40:38.849 [info] > git status -z -uall [19ms]
2025-06-02 16:40:38.850 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:40:43.888 [info] > git config --get commit.template [15ms]
2025-06-02 16:40:43.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:40:43.920 [info] > git status -z -uall [13ms]
2025-06-02 16:40:43.922 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:40:48.954 [info] > git config --get commit.template [11ms]
2025-06-02 16:40:48.955 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:40:48.983 [info] > git status -z -uall [14ms]
2025-06-02 16:40:48.985 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:40:54.018 [info] > git config --get commit.template [13ms]
2025-06-02 16:40:54.020 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:40:54.040 [info] > git status -z -uall [9ms]
2025-06-02 16:40:54.041 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:40:59.067 [info] > git config --get commit.template [2ms]
2025-06-02 16:40:59.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:40:59.131 [info] > git status -z -uall [19ms]
2025-06-02 16:40:59.133 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:41:04.178 [info] > git config --get commit.template [22ms]
2025-06-02 16:41:04.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:41:04.224 [info] > git status -z -uall [21ms]
2025-06-02 16:41:04.226 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:41:22.776 [info] > git config --get commit.template [9ms]
2025-06-02 16:41:22.777 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:41:22.803 [info] > git status -z -uall [12ms]
2025-06-02 16:41:22.805 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:41:27.840 [info] > git config --get commit.template [16ms]
2025-06-02 16:41:27.841 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:41:27.866 [info] > git status -z -uall [12ms]
2025-06-02 16:41:27.867 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:41:44.197 [info] > git config --get commit.template [33ms]
2025-06-02 16:41:44.199 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:41:44.258 [info] > git status -z -uall [36ms]
2025-06-02 16:41:44.260 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:41:49.315 [info] > git config --get commit.template [20ms]
2025-06-02 16:41:49.316 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:41:49.363 [info] > git status -z -uall [25ms]
2025-06-02 16:41:49.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:41:54.403 [info] > git config --get commit.template [17ms]
2025-06-02 16:41:54.405 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:41:54.436 [info] > git status -z -uall [14ms]
2025-06-02 16:41:54.437 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:41:59.487 [info] > git config --get commit.template [26ms]
2025-06-02 16:41:59.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:41:59.529 [info] > git status -z -uall [20ms]
2025-06-02 16:41:59.530 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:42:06.525 [info] > git config --get commit.template [21ms]
2025-06-02 16:42:06.527 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:42:06.558 [info] > git status -z -uall [12ms]
2025-06-02 16:42:06.559 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:42:11.601 [info] > git config --get commit.template [5ms]
2025-06-02 16:42:11.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:42:11.674 [info] > git status -z -uall [23ms]
2025-06-02 16:42:11.677 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:42:16.705 [info] > git config --get commit.template [4ms]
2025-06-02 16:42:16.725 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:42:16.771 [info] > git status -z -uall [21ms]
2025-06-02 16:42:16.772 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:42:21.863 [info] > git config --get commit.template [2ms]
2025-06-02 16:42:21.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:42:21.936 [info] > git status -z -uall [25ms]
2025-06-02 16:42:21.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:42:26.997 [info] > git config --get commit.template [25ms]
2025-06-02 16:42:27.001 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:42:27.042 [info] > git status -z -uall [22ms]
2025-06-02 16:42:27.046 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:42:32.087 [info] > git config --get commit.template [17ms]
2025-06-02 16:42:32.090 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:42:32.142 [info] > git status -z -uall [26ms]
2025-06-02 16:42:32.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:42:37.185 [info] > git config --get commit.template [17ms]
2025-06-02 16:42:37.186 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:42:37.240 [info] > git status -z -uall [31ms]
2025-06-02 16:42:37.241 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:42:42.276 [info] > git config --get commit.template [14ms]
2025-06-02 16:42:42.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:42:42.302 [info] > git status -z -uall [12ms]
2025-06-02 16:42:42.303 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:42:47.339 [info] > git config --get commit.template [17ms]
2025-06-02 16:42:47.340 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:42:47.375 [info] > git status -z -uall [18ms]
2025-06-02 16:42:47.376 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:42:52.411 [info] > git config --get commit.template [13ms]
2025-06-02 16:42:52.412 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:42:52.436 [info] > git status -z -uall [12ms]
2025-06-02 16:42:52.437 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:42:57.517 [info] > git config --get commit.template [40ms]
2025-06-02 16:42:57.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:42:57.592 [info] > git status -z -uall [43ms]
2025-06-02 16:42:57.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:43:02.622 [info] > git config --get commit.template [1ms]
2025-06-02 16:43:02.649 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:43:02.700 [info] > git status -z -uall [24ms]
2025-06-02 16:43:02.702 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:43:07.743 [info] > git config --get commit.template [1ms]
2025-06-02 16:43:07.776 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:43:07.849 [info] > git status -z -uall [41ms]
2025-06-02 16:43:07.851 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:43:12.976 [info] > git config --get commit.template [32ms]
2025-06-02 16:43:12.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:43:13.042 [info] > git status -z -uall [31ms]
2025-06-02 16:43:13.044 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:43:18.091 [info] > git config --get commit.template [23ms]
2025-06-02 16:43:18.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:43:18.143 [info] > git status -z -uall [26ms]
2025-06-02 16:43:18.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:43:26.031 [info] > git config --get commit.template [1ms]
2025-06-02 16:43:26.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:43:26.127 [info] > git status -z -uall [29ms]
2025-06-02 16:43:26.128 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:43:31.173 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:43:31.173 [info] > git config --get commit.template [24ms]
2025-06-02 16:43:31.218 [info] > git status -z -uall [20ms]
2025-06-02 16:43:31.219 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:43:36.265 [info] > git config --get commit.template [18ms]
2025-06-02 16:43:36.266 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:43:36.298 [info] > git status -z -uall [16ms]
2025-06-02 16:43:36.299 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:43:41.341 [info] > git config --get commit.template [20ms]
2025-06-02 16:43:41.342 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:43:41.378 [info] > git status -z -uall [18ms]
2025-06-02 16:43:41.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:43:46.405 [info] > git config --get commit.template [1ms]
2025-06-02 16:43:46.435 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-02 16:43:46.468 [info] > git status -z -uall [14ms]
2025-06-02 16:43:46.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:43:51.519 [info] > git config --get commit.template [17ms]
2025-06-02 16:43:51.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:43:51.561 [info] > git status -z -uall [25ms]
2025-06-02 16:43:51.561 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:43:56.587 [info] > git config --get commit.template [4ms]
2025-06-02 16:43:56.606 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:43:56.635 [info] > git status -z -uall [11ms]
2025-06-02 16:43:56.635 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:44:01.980 [info] > git config --get commit.template [2ms]
2025-06-02 16:44:02.028 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:44:02.218 [info] > git status -z -uall [116ms]
2025-06-02 16:44:02.226 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [21ms]
2025-06-02 16:44:07.277 [info] > git config --get commit.template [22ms]
2025-06-02 16:44:07.278 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:44:07.334 [info] > git status -z -uall [36ms]
2025-06-02 16:44:07.336 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:44:12.368 [info] > git config --get commit.template [11ms]
2025-06-02 16:44:12.369 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:44:12.390 [info] > git status -z -uall [12ms]
2025-06-02 16:44:12.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:44:17.436 [info] > git config --get commit.template [20ms]
2025-06-02 16:44:17.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:44:17.500 [info] > git status -z -uall [34ms]
2025-06-02 16:44:17.501 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:44:22.531 [info] > git config --get commit.template [10ms]
2025-06-02 16:44:22.532 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:44:22.556 [info] > git status -z -uall [12ms]
2025-06-02 16:44:22.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:44:25.084 [info] > git show --textconv :.env.example [11ms]
2025-06-02 16:44:25.085 [info] > git ls-files --stage -- .env.example [2ms]
2025-06-02 16:44:25.098 [info] > git cat-file -s d814a3cfbdb4b5bf5877f10918b72f0aef0a2980 [1ms]
2025-06-02 16:44:27.594 [info] > git config --get commit.template [17ms]
2025-06-02 16:44:27.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:44:27.626 [info] > git status -z -uall [17ms]
2025-06-02 16:44:27.627 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:44:32.658 [info] > git config --get commit.template [12ms]
2025-06-02 16:44:32.659 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:44:32.680 [info] > git status -z -uall [10ms]
2025-06-02 16:44:32.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:44:37.755 [info] > git config --get commit.template [32ms]
2025-06-02 16:44:37.758 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:44:37.820 [info] > git status -z -uall [29ms]
2025-06-02 16:44:37.822 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:44:47.988 [info] > git config --get commit.template [18ms]
2025-06-02 16:44:47.989 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:44:48.024 [info] > git status -z -uall [17ms]
2025-06-02 16:44:48.025 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:44:53.062 [info] > git config --get commit.template [17ms]
2025-06-02 16:44:53.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:44:53.105 [info] > git status -z -uall [17ms]
2025-06-02 16:44:53.106 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:44:58.133 [info] > git config --get commit.template [9ms]
2025-06-02 16:44:58.134 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:44:58.153 [info] > git status -z -uall [8ms]
2025-06-02 16:44:58.155 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:45:03.187 [info] > git config --get commit.template [12ms]
2025-06-02 16:45:03.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:45:03.208 [info] > git status -z -uall [9ms]
2025-06-02 16:45:03.209 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:45:08.251 [info] > git config --get commit.template [18ms]
2025-06-02 16:45:08.252 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:45:08.282 [info] > git status -z -uall [14ms]
2025-06-02 16:45:08.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:45:13.316 [info] > git config --get commit.template [13ms]
2025-06-02 16:45:13.317 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:45:13.342 [info] > git status -z -uall [13ms]
2025-06-02 16:45:13.343 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:45:18.367 [info] > git config --get commit.template [3ms]
2025-06-02 16:45:18.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:45:18.408 [info] > git status -z -uall [15ms]
2025-06-02 16:45:18.410 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:45:23.443 [info] > git config --get commit.template [12ms]
2025-06-02 16:45:23.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:45:23.463 [info] > git status -z -uall [11ms]
2025-06-02 16:45:23.464 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:45:28.518 [info] > git config --get commit.template [26ms]
2025-06-02 16:45:28.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:45:28.558 [info] > git status -z -uall [18ms]
2025-06-02 16:45:28.560 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:45:33.603 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:45:33.604 [info] > git config --get commit.template [24ms]
2025-06-02 16:45:33.646 [info] > git status -z -uall [13ms]
2025-06-02 16:45:33.648 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:45:38.680 [info] > git config --get commit.template [12ms]
2025-06-02 16:45:38.681 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:45:38.710 [info] > git status -z -uall [17ms]
2025-06-02 16:45:38.711 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:45:43.743 [info] > git config --get commit.template [12ms]
2025-06-02 16:45:43.744 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:45:43.801 [info] > git status -z -uall [41ms]
2025-06-02 16:45:43.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 16:45:49.664 [info] > git config --get commit.template [11ms]
2025-06-02 16:45:49.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:45:49.691 [info] > git status -z -uall [13ms]
2025-06-02 16:45:49.692 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:45:54.730 [info] > git config --get commit.template [14ms]
2025-06-02 16:45:54.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:45:54.752 [info] > git status -z -uall [10ms]
2025-06-02 16:45:54.753 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:45:59.781 [info] > git config --get commit.template [9ms]
2025-06-02 16:45:59.782 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:45:59.802 [info] > git status -z -uall [10ms]
2025-06-02 16:45:59.803 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:46:04.844 [info] > git config --get commit.template [16ms]
2025-06-02 16:46:04.846 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:46:04.891 [info] > git status -z -uall [24ms]
2025-06-02 16:46:04.891 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:46:09.951 [info] > git config --get commit.template [18ms]
2025-06-02 16:46:09.954 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 16:46:09.988 [info] > git status -z -uall [12ms]
2025-06-02 16:46:09.989 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:46:15.026 [info] > git config --get commit.template [14ms]
2025-06-02 16:46:15.027 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:46:15.046 [info] > git status -z -uall [8ms]
2025-06-02 16:46:15.048 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:46:20.099 [info] > git config --get commit.template [20ms]
2025-06-02 16:46:20.100 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:46:20.136 [info] > git status -z -uall [17ms]
2025-06-02 16:46:20.137 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:46:25.317 [info] > git config --get commit.template [150ms]
2025-06-02 16:46:25.343 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:46:25.398 [info] > git status -z -uall [31ms]
2025-06-02 16:46:25.401 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-02 16:46:30.440 [info] > git config --get commit.template [14ms]
2025-06-02 16:46:30.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:46:30.468 [info] > git status -z -uall [14ms]
2025-06-02 16:46:30.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:46:35.500 [info] > git config --get commit.template [6ms]
2025-06-02 16:46:35.524 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:46:35.556 [info] > git status -z -uall [11ms]
2025-06-02 16:46:35.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:46:40.607 [info] > git config --get commit.template [21ms]
2025-06-02 16:46:40.609 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:46:40.639 [info] > git status -z -uall [14ms]
2025-06-02 16:46:40.640 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:46:45.674 [info] > git config --get commit.template [13ms]
2025-06-02 16:46:45.674 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:46:45.695 [info] > git status -z -uall [9ms]
2025-06-02 16:46:45.696 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:46:50.727 [info] > git config --get commit.template [2ms]
2025-06-02 16:46:50.748 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:46:50.772 [info] > git status -z -uall [13ms]
2025-06-02 16:46:50.773 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:46:55.806 [info] > git config --get commit.template [14ms]
2025-06-02 16:46:55.807 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:46:55.831 [info] > git status -z -uall [12ms]
2025-06-02 16:46:55.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:47:17.471 [info] > git config --get commit.template [12ms]
2025-06-02 16:47:17.472 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:47:17.492 [info] > git status -z -uall [9ms]
2025-06-02 16:47:17.493 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:47:22.547 [info] > git config --get commit.template [24ms]
2025-06-02 16:47:22.550 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:47:22.586 [info] > git status -z -uall [19ms]
2025-06-02 16:47:22.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:47:28.942 [info] > git config --get commit.template [20ms]
2025-06-02 16:47:28.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:47:28.976 [info] > git status -z -uall [15ms]
2025-06-02 16:47:28.978 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:47:34.014 [info] > git config --get commit.template [15ms]
2025-06-02 16:47:34.015 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:47:34.038 [info] > git status -z -uall [9ms]
2025-06-02 16:47:34.039 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:47:39.075 [info] > git config --get commit.template [16ms]
2025-06-02 16:47:39.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:47:39.116 [info] > git status -z -uall [22ms]
2025-06-02 16:47:39.117 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:47:44.143 [info] > git config --get commit.template [9ms]
2025-06-02 16:47:44.144 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:47:44.163 [info] > git status -z -uall [8ms]
2025-06-02 16:47:44.164 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:47:49.206 [info] > git config --get commit.template [19ms]
2025-06-02 16:47:49.207 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:47:49.232 [info] > git status -z -uall [9ms]
2025-06-02 16:47:49.233 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:47:54.285 [info] > git config --get commit.template [27ms]
2025-06-02 16:47:54.286 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:47:54.323 [info] > git status -z -uall [19ms]
2025-06-02 16:47:54.325 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:47:59.361 [info] > git config --get commit.template [18ms]
2025-06-02 16:47:59.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 16:47:59.386 [info] > git status -z -uall [10ms]
2025-06-02 16:47:59.387 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:48:04.416 [info] > git config --get commit.template [10ms]
2025-06-02 16:48:04.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:48:04.435 [info] > git status -z -uall [9ms]
2025-06-02 16:48:04.436 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:48:09.473 [info] > git config --get commit.template [17ms]
2025-06-02 16:48:09.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:48:09.509 [info] > git status -z -uall [13ms]
2025-06-02 16:48:09.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:48:14.564 [info] > git config --get commit.template [21ms]
2025-06-02 16:48:14.566 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:48:14.612 [info] > git status -z -uall [26ms]
2025-06-02 16:48:14.613 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 16:48:19.649 [info] > git config --get commit.template [17ms]
2025-06-02 16:48:19.650 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:48:19.676 [info] > git status -z -uall [14ms]
2025-06-02 16:48:19.677 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:48:24.718 [info] > git config --get commit.template [1ms]
2025-06-02 16:48:24.751 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:48:24.916 [info] > git status -z -uall [133ms]
2025-06-02 16:48:24.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [72ms]
2025-06-02 16:48:29.952 [info] > git config --get commit.template [13ms]
2025-06-02 16:48:29.954 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:48:29.988 [info] > git status -z -uall [17ms]
2025-06-02 16:48:29.989 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 16:48:38.799 [info] > git config --get commit.template [13ms]
2025-06-02 16:48:38.800 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:48:38.824 [info] > git status -z -uall [10ms]
2025-06-02 16:48:38.825 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:48:40.897 [info] > git show --textconv :.replit [19ms]
2025-06-02 16:48:40.897 [info] > git ls-files --stage -- .replit [3ms]
2025-06-02 16:48:40.919 [info] > git cat-file -s 94429ce8e752f0414fc449966fbe9040ae43a887 [2ms]
2025-06-02 16:48:43.858 [info] > git config --get commit.template [16ms]
2025-06-02 16:48:43.858 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:48:43.884 [info] > git status -z -uall [12ms]
2025-06-02 16:48:43.886 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:48:48.971 [info] > git config --get commit.template [53ms]
2025-06-02 16:48:48.996 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:48:49.026 [info] > git status -z -uall [16ms]
2025-06-02 16:48:49.027 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:48:54.070 [info] > git config --get commit.template [19ms]
2025-06-02 16:48:54.071 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:48:54.108 [info] > git status -z -uall [19ms]
2025-06-02 16:48:54.109 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:48:54.231 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- .replit [7ms]
2025-06-02 16:48:59.139 [info] > git config --get commit.template [1ms]
2025-06-02 16:48:59.186 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:48:59.250 [info] > git status -z -uall [39ms]
2025-06-02 16:48:59.251 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-06-02 16:49:04.311 [info] > git config --get commit.template [29ms]
2025-06-02 16:49:04.313 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:49:04.359 [info] > git status -z -uall [28ms]
2025-06-02 16:49:04.360 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:49:09.388 [info] > git config --get commit.template [9ms]
2025-06-02 16:49:09.389 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:49:09.413 [info] > git status -z -uall [14ms]
2025-06-02 16:49:09.414 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:49:14.439 [info] > git config --get commit.template [9ms]
2025-06-02 16:49:14.440 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:49:14.456 [info] > git status -z -uall [8ms]
2025-06-02 16:49:14.458 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:49:19.492 [info] > git config --get commit.template [15ms]
2025-06-02 16:49:19.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:49:19.514 [info] > git status -z -uall [10ms]
2025-06-02 16:49:19.515 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:49:24.560 [info] > git config --get commit.template [19ms]
2025-06-02 16:49:24.562 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:49:24.598 [info] > git status -z -uall [19ms]
2025-06-02 16:49:24.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:49:29.631 [info] > git config --get commit.template [13ms]
2025-06-02 16:49:29.632 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:49:29.652 [info] > git status -z -uall [10ms]
2025-06-02 16:49:29.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:49:34.681 [info] > git config --get commit.template [9ms]
2025-06-02 16:49:34.682 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:49:34.704 [info] > git status -z -uall [10ms]
2025-06-02 16:49:34.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:49:39.731 [info] > git config --get commit.template [5ms]
2025-06-02 16:49:39.755 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:49:39.782 [info] > git status -z -uall [12ms]
2025-06-02 16:49:39.783 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:49:41.379 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-02 16:49:43.266 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-02 16:49:44.456 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-02 16:49:44.836 [info] > git config --get commit.template [30ms]
2025-06-02 16:49:44.837 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:49:44.871 [info] > git status -z -uall [14ms]
2025-06-02 16:49:44.872 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:49:49.904 [info] > git config --get commit.template [11ms]
2025-06-02 16:49:49.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:49:49.938 [info] > git status -z -uall [19ms]
2025-06-02 16:49:49.939 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:49:54.990 [info] > git config --get commit.template [26ms]
2025-06-02 16:49:54.992 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:49:55.053 [info] > git status -z -uall [27ms]
2025-06-02 16:49:55.054 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:50:00.103 [info] > git config --get commit.template [20ms]
2025-06-02 16:50:00.104 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:50:00.145 [info] > git status -z -uall [20ms]
2025-06-02 16:50:00.146 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:50:05.178 [info] > git config --get commit.template [3ms]
2025-06-02 16:50:05.207 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 16:50:05.251 [info] > git status -z -uall [18ms]
2025-06-02 16:50:05.251 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:50:10.478 [info] > git config --get commit.template [38ms]
2025-06-02 16:50:10.481 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-02 16:50:10.555 [info] > git status -z -uall [35ms]
2025-06-02 16:50:10.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:50:15.647 [info] > git config --get commit.template [5ms]
2025-06-02 16:50:15.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-02 16:50:15.750 [info] > git status -z -uall [33ms]
2025-06-02 16:50:15.753 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 16:50:20.792 [info] > git config --get commit.template [15ms]
2025-06-02 16:50:20.793 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:50:20.825 [info] > git status -z -uall [16ms]
2025-06-02 16:50:20.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:50:26.349 [info] > git config --get commit.template [2ms]
2025-06-02 16:50:26.375 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:50:26.413 [info] > git status -z -uall [19ms]
2025-06-02 16:50:26.415 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:50:27.801 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-02 16:50:31.461 [info] > git config --get commit.template [21ms]
2025-06-02 16:50:31.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:50:31.506 [info] > git status -z -uall [24ms]
2025-06-02 16:50:31.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:50:36.527 [info] > git config --get commit.template [2ms]
2025-06-02 16:50:36.542 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:50:36.580 [info] > git status -z -uall [22ms]
2025-06-02 16:50:36.581 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:50:41.610 [info] > git config --get commit.template [10ms]
2025-06-02 16:50:41.611 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:50:41.634 [info] > git status -z -uall [10ms]
2025-06-02 16:50:41.635 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:50:46.669 [info] > git config --get commit.template [14ms]
2025-06-02 16:50:46.670 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:50:46.692 [info] > git status -z -uall [11ms]
2025-06-02 16:50:46.693 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:50:54.427 [info] > git config --get commit.template [5ms]
2025-06-02 16:50:54.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:50:54.464 [info] > git status -z -uall [9ms]
2025-06-02 16:50:54.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 16:50:59.542 [info] > git config --get commit.template [33ms]
2025-06-02 16:50:59.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 16:50:59.601 [info] > git status -z -uall [33ms]
2025-06-02 16:50:59.604 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:51:04.646 [info] > git config --get commit.template [18ms]
2025-06-02 16:51:04.648 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:51:04.680 [info] > git status -z -uall [12ms]
2025-06-02 16:51:04.682 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:51:09.743 [info] > git config --get commit.template [4ms]
2025-06-02 16:51:09.786 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:51:09.904 [info] > git status -z -uall [93ms]
2025-06-02 16:51:09.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [120ms]
2025-06-02 16:51:15.005 [info] > git config --get commit.template [22ms]
2025-06-02 16:51:15.006 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:51:15.069 [info] > git status -z -uall [33ms]
2025-06-02 16:51:15.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:51:20.138 [info] > git config --get commit.template [35ms]
2025-06-02 16:51:20.139 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:51:20.187 [info] > git status -z -uall [24ms]
2025-06-02 16:51:20.187 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:51:25.232 [info] > git config --get commit.template [23ms]
2025-06-02 16:51:25.234 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:51:25.267 [info] > git status -z -uall [15ms]
2025-06-02 16:51:25.268 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:51:30.312 [info] > git config --get commit.template [19ms]
2025-06-02 16:51:30.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:51:30.357 [info] > git status -z -uall [23ms]
2025-06-02 16:51:30.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:51:35.548 [info] > git config --get commit.template [145ms]
2025-06-02 16:51:35.575 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:51:35.631 [info] > git status -z -uall [36ms]
2025-06-02 16:51:35.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-02 16:51:40.723 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:51:40.724 [info] > git config --get commit.template [46ms]
2025-06-02 16:51:40.880 [info] > git status -z -uall [99ms]
2025-06-02 16:51:40.886 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 16:51:45.935 [info] > git config --get commit.template [22ms]
2025-06-02 16:51:45.936 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:51:45.984 [info] > git status -z -uall [21ms]
2025-06-02 16:51:45.986 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 16:51:51.021 [info] > git config --get commit.template [15ms]
2025-06-02 16:51:51.022 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:51:51.046 [info] > git status -z -uall [9ms]
2025-06-02 16:51:51.047 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:51:56.080 [info] > git config --get commit.template [12ms]
2025-06-02 16:51:56.081 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:51:56.106 [info] > git status -z -uall [12ms]
2025-06-02 16:51:56.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:52:01.176 [info] > git config --get commit.template [49ms]
2025-06-02 16:52:01.203 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:52:01.257 [info] > git status -z -uall [21ms]
2025-06-02 16:52:01.257 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 16:52:06.287 [info] > git config --get commit.template [11ms]
2025-06-02 16:52:06.288 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 16:52:06.314 [info] > git status -z -uall [14ms]
2025-06-02 16:52:06.315 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:52:11.368 [info] > git config --get commit.template [22ms]
2025-06-02 16:52:11.369 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:52:11.397 [info] > git status -z -uall [12ms]
2025-06-02 16:52:11.398 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:52:16.441 [info] > git config --get commit.template [21ms]
2025-06-02 16:52:16.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:52:16.482 [info] > git status -z -uall [22ms]
2025-06-02 16:52:16.483 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:52:21.518 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 16:52:21.518 [info] > git config --get commit.template [11ms]
2025-06-02 16:52:21.536 [info] > git status -z -uall [8ms]
2025-06-02 16:52:21.537 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:52:26.573 [info] > git config --get commit.template [16ms]
2025-06-02 16:52:26.575 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:52:26.600 [info] > git status -z -uall [11ms]
2025-06-02 16:52:26.602 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:52:31.642 [info] > git config --get commit.template [17ms]
2025-06-02 16:52:31.643 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:52:31.671 [info] > git status -z -uall [11ms]
2025-06-02 16:52:31.672 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 16:52:36.707 [info] > git config --get commit.template [3ms]
2025-06-02 16:52:36.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 16:52:36.848 [info] > git status -z -uall [31ms]
2025-06-02 16:52:36.848 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 16:52:41.914 [info] > git config --get commit.template [29ms]
2025-06-02 16:52:41.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 16:52:41.968 [info] > git status -z -uall [23ms]
2025-06-02 16:52:41.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
