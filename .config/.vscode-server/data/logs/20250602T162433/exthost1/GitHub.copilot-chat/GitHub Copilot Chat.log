2025-06-02 16:24:43.784 [info] Can't use the Electron fetcher in this environment.
2025-06-02 16:24:43.784 [info] Using the Node fetch fetcher.
2025-06-02 16:24:43.784 [info] Initializing Git extension service.
2025-06-02 16:24:43.784 [info] Successfully activated the vscode.git extension.
2025-06-02 16:24:43.784 [info] Enablement state of the vscode.git extension: true.
2025-06-02 16:24:43.784 [info] Successfully registered Git commit message provider.
2025-06-02 16:24:44.811 [info] Logged in as Chewy42
2025-06-02 16:24:45.570 [info] Got Copilot token for Chewy42
2025-06-02 16:24:46.017 [info] Fetched model metadata in 439ms 27dfa05c-28b8-460f-a8d8-ac8da1e74cc4
2025-06-02 16:24:46.031 [info] activationBlocker from 'languageModelAccess' took for 2177ms
2025-06-02 16:24:46.237 [info] copilot token chat_enabled: true, sku: free_educational
2025-06-02 16:24:46.252 [info] Registering default platform agent...
2025-06-02 16:24:46.487 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-02 16:24:46.487 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-02 16:24:46.487 [info] Successfully registered GitHub PR title and description provider.
2025-06-02 16:24:46.487 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-02 16:24:46.613 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-06-02 16:26:18.140 [info] TypeScript server plugin activated.
2025-06-02 16:26:18.142 [info] Registered TypeScript context provider with Copilot inline completions.
2025-06-02 16:39:26.204 [info] Fetched model metadata in 504ms 8b231678-ac28-4c4d-8727-98a0aaad859b
2025-06-02 16:42:11.131 [info] message 0 returned. finish reason: [stop]
2025-06-02 16:42:11.132 [info] request done: requestId: [89444792-f497-4047-bc2a-75ceed5b93ed] model deployment ID: []
2025-06-02 16:42:21.239 [info] message 0 returned. finish reason: [stop]
2025-06-02 16:42:21.239 [info] request done: requestId: [a000118b-ee92-40b8-a1da-341897196f58] model deployment ID: []
2025-06-02 16:42:22.673 [info] message 0 returned. finish reason: [stop]
2025-06-02 16:42:22.673 [info] request done: requestId: [bb41fccd-ccb2-4967-88c9-fc4365f6ec72] model deployment ID: []
2025-06-02 16:43:15.988 [info] message 0 returned. finish reason: [stop]
2025-06-02 16:43:15.988 [info] request done: requestId: [7c26a985-026a-4761-943b-9d936157a087] model deployment ID: []
2025-06-02 16:43:25.429 [info] message 0 returned. finish reason: [stop]
2025-06-02 16:43:25.429 [info] request done: requestId: [0d8da71e-1f6d-416b-bf93-cde1a2d39b2f] model deployment ID: []
2025-06-02 16:54:48.032 [info] Logged in as Chewy42
2025-06-02 16:54:48.658 [info] Got Copilot token for Chewy42
2025-06-02 16:54:49.344 [info] Fetched model metadata in 683ms bf84e56d-3e64-4546-b93a-614ac2bcbe7f
2025-06-02 16:54:49.617 [info] copilot token chat_enabled: true, sku: free_educational
2025-06-02 16:54:49.743 [info] BYOK: Copilot Chat known models list fetched successfully.
