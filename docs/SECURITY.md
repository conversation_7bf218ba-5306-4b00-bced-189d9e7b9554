# ChewyAI Security Documentation

## 🔐 Security Architecture

### API Key Management

#### Client-Side Security
- **NO API keys are stored on the client-side**
- User-provided AI credentials are sent to backend for ephemeral use only
- Supabase anonymous key is safe for client-side use (read-only access)
- All sensitive operations require authentication

#### Backend Security
- User AI provider credentials are handled in-memory only during API calls
- No logging of sensitive credentials (API keys, tokens)
- Backend-owned credentials managed via environment variables
- Proper input validation and sanitization

### Authentication & Authorization

#### Supabase Authentication
- JWT-based authentication using Supabase Auth
- Row Level Security (RLS) policies enforce data access
- Session management handled by Supabase client
- Automatic token refresh

#### API Protection
- All protected endpoints require valid JWT token
- Authorization header: `Bearer <jwt_token>`
- User context extracted from token for data access
- Proper error handling for unauthorized requests

## 🛡️ Security Headers

### Production Security Headers
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
Content-Security-Policy: [configured for app requirements]
```

### CORS Configuration
- Configured for specific origins in production
- Credentials allowed for authenticated requests
- Proper preflight handling for complex requests

## 🔒 Environment Variables

### Required Production Variables
```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Client Environment Variables
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_API_BASE_URL=https://your-domain.com/api

# Application Configuration
NODE_ENV=production
PORT=80
```

### Security Best Practices
- Never commit `.env` files to version control
- Use different keys for development and production
- Rotate keys regularly
- Monitor for exposed credentials in logs

## 🚨 Data Protection

### User Data Handling
- Text extraction performed client-side when possible
- Content sent to self-hosted backend, then to user's AI provider
- No persistent storage of user AI credentials
- User data stored in Supabase with RLS policies

### AI Provider Integration
- User's own API keys used for AI requests
- No ChewyAI-owned AI credits exposed to users
- Transparent data flow: Client → ChewyAI Backend → User's AI Provider
- No caching of AI responses containing user data

## 🔍 Security Monitoring

### Logging Practices
- Log authentication attempts and failures
- Monitor API usage patterns
- Never log sensitive credentials or user data
- Implement proper error handling without exposing internals

### Health Checks
- `/api/health` endpoint for monitoring
- No sensitive information in health check responses
- Proper error responses without stack traces in production

## 🛠️ Development Security

### Local Development
- Use `.env.example` as template for local environment
- Never use production credentials in development
- Test with development Supabase project
- Validate security headers in development

### Code Security
- Input validation using Zod schemas
- SQL injection prevention with parameterized queries
- XSS prevention with proper output encoding
- CSRF protection for state-changing operations

## 📋 Security Checklist

### Pre-Deployment
- [ ] All environment variables configured
- [ ] No hardcoded credentials in code
- [ ] Security headers implemented
- [ ] CORS properly configured
- [ ] Authentication working correctly
- [ ] Input validation in place
- [ ] Error handling doesn't expose internals

### Post-Deployment
- [ ] Health check endpoint responding
- [ ] Authentication flow working
- [ ] API endpoints properly protected
- [ ] Security headers present in responses
- [ ] No sensitive data in logs
- [ ] HTTPS enforced (handled by Replit)

## 🚨 Incident Response

### If Credentials Are Compromised
1. Immediately rotate affected credentials
2. Update environment variables in deployment
3. Redeploy application
4. Monitor for unauthorized access
5. Notify users if necessary

### Security Updates
- Monitor dependencies for security vulnerabilities
- Update packages regularly
- Test security updates in development first
- Document security-related changes
